/**
 * 图标选择器组件
 * 集成现有图标管理系统的完整功能：库、分类、收藏夹、搜索等
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Input,
  Select,
  Row,
  Col,
  Card,
  Pagination,
  Empty,
  Spin,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Tabs,
  Tree,
  Badge,
  Radio
} from 'antd';
import {
  SearchOutlined,
  ClearOutlined,
  StarOutlined,
  StarFilled,
  AppstoreOutlined,
  UnorderedListOutlined,
  HeartOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { debounce } from 'lodash';
import axiosInstance from '../../api/axiosInstance';
import UniversalIconRenderer from './UniversalIconRenderer';
import { addIconToFavorites, removeIconFromFavorites, initializeFavoritesSystem } from '../../utils/iconFavoritesHelper';

const { Search } = Input;
const { Option } = Select;

interface IconSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect?: (iconValue: string) => void;
  onInsert?: (iconData: IconInsertData) => void;
  value?: string;
  size?: number;
  theme?: 'light' | 'dark';
  title?: string;
  // 新增的UniversalIconSelector功能
  mode?: 'select' | 'insert';
  insertMode?: 'markdown' | 'component' | 'url' | 'svg';
  multiple?: boolean;
  libraries?: string[];
  categories?: string[];
  width?: number;
  height?: number;
}

interface IconInsertData {
  markdown?: string;
  component?: string;
  url?: string;
  svg?: string;
  icon: IconMetadata;
}

interface IconLibrary {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  sort_order: number;
}

interface IconCategory {
  id: number;
  library_id: number;
  name: string;
  display_name: string;
  description?: string;
  color?: string;
  icon?: string;
  parent_id?: number;
  children?: IconCategory[];
}

interface IconMetadata {
  id: number;
  library_id: number;
  category_id?: number;
  icon_key: string;
  display_name?: string;
  description?: string;
  tags?: string[];
  usage_count: number;
  is_favorite: boolean;
  library?: IconLibrary;
  category?: IconCategory;
}

interface SearchFilters {
  library_id?: number;
  category_id?: number;
  search?: string;
  favorites_only?: boolean;
}

const IconSelector: React.FC<IconSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  onInsert,
  value,
  size = 32,
  theme = 'light',
  title = '选择图标',
  // 新增的功能参数
  mode = 'select',
  insertMode = 'markdown',
  multiple = false,
  libraries = ['lucide', 'iconic', 'iconify'],
  categories = [],
  width = 1200,
  height = 700
}) => {
  const [activeTab, setActiveTab] = useState('browse');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(24);
  const [selectedIcon, setSelectedIcon] = useState<string | null>(value || null);
  const [selectedIcons, setSelectedIcons] = useState<IconMetadata[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFavoriteFolder, setSelectedFavoriteFolder] = useState<number | null>(null);

  // 本地搜索输入状态（用于即时响应）
  const [localSearchValue, setLocalSearchValue] = useState('');
  const queryClient = useQueryClient();

  // 获取图标库列表
  const { data: libraries, isLoading: librariesLoading } = useQuery({
    queryKey: ['icon-libraries'],
    queryFn: async () => {
      const response = await axiosInstance.get('/icons/libraries');
      return response.data;
    },
    enabled: visible,
    staleTime: 300000, // 5分钟内不重新请求
    gcTime: 1800000 // 30分钟后清理缓存
  });

  // 获取分类列表
  const { data: categories } = useQuery({
    queryKey: ['icon-categories', searchFilters.library_id],
    queryFn: async () => {
      const params = searchFilters.library_id ? { library_id: searchFilters.library_id } : {};
      const response = await axiosInstance.get('/icons/categories', { params });
      return response.data;
    },
    enabled: visible && !!searchFilters.library_id,
    staleTime: 180000, // 3分钟内不重新请求
    gcTime: 900000 // 15分钟后清理缓存
  });

  // 搜索图标
  const { data: searchResult, isLoading: iconsLoading } = useQuery({
    queryKey: ['icon-search', searchFilters, currentPage, pageSize, activeTab],
    queryFn: async () => {
      try {
        // 简化的搜索参数
        const searchParams: any = {
          query: searchFilters.search || '',
          page: currentPage,
          page_size: pageSize,
          sort_by: 'usage_count',
          sort_order: 'desc'
        };

        // 如果在浏览图标标签页且没有任何筛选条件，显示热门图标
        if (activeTab === 'browse' && !searchFilters.search && !searchFilters.library_id && !searchFilters.category_id) {
          searchParams.query = '*'; // 显示所有图标
        }

        // 添加库筛选
        if (searchFilters.library_id && libraries) {
          const library = libraries.find(lib => lib.id === searchFilters.library_id);
          if (library) {
            searchParams.library = library.name;
          }
        }

        // 添加分类筛选
        if (searchFilters.category_id && categories) {
          const category = categories.find(cat => cat.id === searchFilters.category_id);
          if (category) {
            searchParams.category = category.name;
          }
        }

        // 收藏筛选
        if (activeTab === 'favorites' || searchFilters.favorites_only) {
          searchParams.favorites_only = true;
        }

        console.log('Searching icons with params:', searchParams);
        const response = await axiosInstance.post('/icons/search', searchParams);
        console.log('Search response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Icon search error:', error);
        return { icons: [], total: 0 };
      }
    },
    enabled: visible && (activeTab === 'browse' || activeTab === 'favorites'),
    staleTime: 30000, // 30秒内不重新请求
    gcTime: 300000 // 5分钟后清理缓存
  });

  // 获取收藏夹列表
  const { data: favoriteFolders = [], isLoading: foldersLoading } = useQuery({
    queryKey: ['favorite-folders'],
    queryFn: async () => {
      console.log('Fetching favorite folders...');
      try {
        // 首先初始化收藏夹系统（确保有默认收藏夹）
        await initializeFavoritesSystem();

        const response = await axiosInstance.get('/favorites/folders');
        console.log('Favorite folders response:', response.data);
        return response.data;
      } catch (error) {
        console.error('获取收藏夹失败:', error);
        return [];
      }
    },
    enabled: visible,
    staleTime: 60000, // 1分钟内不重新请求
    gcTime: 600000 // 10分钟后清理缓存
  });

  // 获取收藏图标（从所有收藏夹中）
  const { data: favoriteIcons = [], isLoading: favoriteIconsLoading } = useQuery({
    queryKey: ['icon-favorites', selectedFavoriteFolder, favoriteFolders.length],
    queryFn: async () => {
      console.log('Fetching favorite icons...', {
        selectedFavoriteFolder,
        favoriteFoldersCount: favoriteFolders.length,
        favoriteFolders
      });

      // 如果没有收藏夹，直接返回空数组
      if (favoriteFolders.length === 0) {
        console.log('No favorite folders found');
        return [];
      }

      if (!selectedFavoriteFolder) {
        // 如果没有选择收藏夹，返回所有收藏夹的图标
        const allIcons: any[] = [];
        for (const folder of favoriteFolders) {
          try {
            console.log(`Fetching icons from folder ${folder.id}: ${folder.display_name}`);
            const response = await axiosInstance.get(`/favorites/folders/${folder.id}/icons`, {
              params: { page: 1, page_size: 1000 }
            });
            console.log(`Folder ${folder.id} response:`, response.data);
            if (response.data && response.data.icons) {
              allIcons.push(...response.data.icons);
            }
          } catch (error) {
            console.error(`Failed to fetch icons from folder ${folder.id}:`, error);
          }
        }
        console.log('All favorite icons:', allIcons);
        return allIcons;
      } else {
        // 获取特定收藏夹的图标
        try {
          console.log(`Fetching icons from specific folder ${selectedFavoriteFolder}`);
          const response = await axiosInstance.get(`/favorites/folders/${selectedFavoriteFolder}/icons`, {
            params: { page: 1, page_size: 1000 }
          });
          console.log('Specific folder response:', response.data);
          return response.data?.icons || [];
        } catch (error) {
          console.error(`Failed to fetch icons from folder ${selectedFavoriteFolder}:`, error);
          return [];
        }
      }
    },
    enabled: visible && activeTab === 'favorites' && !foldersLoading
  });

  // 获取图标统计
  const { data: iconStats } = useQuery({
    queryKey: ['icon-stats'],
    queryFn: async () => {
      const response = await axiosInstance.get('/icons/stats');
      return response.data;
    },
    enabled: visible
  });

  // 切换收藏状态 - 使用收藏夹管理系统
  const favoriteMutation = useMutation({
    mutationFn: async ({ iconId, isFavorite }: { iconId: number; isFavorite: boolean }) => {
      if (isFavorite) {
        // 从收藏夹中移除图标
        await removeIconFromFavorites(iconId);
      } else {
        // 添加到收藏夹（会自动确保默认收藏夹存在）
        await addIconToFavorites(iconId);
      }
    },
    onSuccess: () => {
      message.success('收藏状态更新成功');
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['icon-search'] });
      queryClient.invalidateQueries({ queryKey: ['icon-favorites'] });
      queryClient.invalidateQueries({ queryKey: ['favorite-folders'] });
    },
    onError: (error: any) => {
      console.error('收藏操作失败:', error);
      message.error('收藏操作失败，请稍后重试');
    }
  });

  // 处理搜索输入 - 立即更新本地状态
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchText = e.target.value;
    // 立即更新本地搜索值以保证输入流畅
    setLocalSearchValue(searchText);
    // 通过防抖来更新实际的搜索过滤器
    debouncedSearch(searchText);
  };

  // 防抖搜索函数
  const debouncedSearch = useCallback(
    debounce((searchText: string) => {
      setSearchFilters(prev => ({ ...prev, search: searchText }));
      setCurrentPage(1);
    }, 200), // 200ms 防抖延迟，更快响应
    []
  );

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // 同步本地搜索值与搜索过滤器（处理外部清空等情况）
  useEffect(() => {
    if (searchFilters.search !== localSearchValue) {
      setLocalSearchValue(searchFilters.search || '');
    }
  }, [searchFilters.search]);

  // 处理搜索按钮点击（保留原有功能）
  const handleSearch = (searchText: string) => {
    setLocalSearchValue(searchText);
    setSearchFilters(prev => ({ ...prev, search: searchText }));
    setCurrentPage(1);
  };

  // 处理图标库筛选
  const handleLibraryFilter = (libraryId: number | undefined) => {
    setSearchFilters(prev => ({
      ...prev,
      library_id: libraryId,
      category_id: undefined // 清除分类筛选
    }));
    setCurrentPage(1);
  };

  // 处理分类筛选
  const handleCategoryFilter = (categoryId: number | undefined) => {
    setSearchFilters(prev => ({ ...prev, category_id: categoryId }));
    setCurrentPage(1);
  };

  // 清除筛选
  const handleClearFilters = () => {
    setSearchFilters({});
    setCurrentPage(1);
  };

  // 生成插入数据的函数
  const generateInsertData = (icon: IconMetadata, insertMode: string): IconInsertData => {
    const baseUrl = `/api/icons/render/${icon.library?.name}/${icon.icon_key}`;

    switch (insertMode) {
      case 'markdown':
        return {
          markdown: `![${icon.display_name || icon.icon_key}](${baseUrl}?theme=${theme}&size=${size})`,
          icon
        };
      case 'component':
        return {
          component: `<CustomIcon name="${icon.library?.name}:${icon.icon_key}" size={${size}} />`,
          icon
        };
      case 'url':
        return {
          url: `${baseUrl}?theme=${theme}&size=${size}`,
          icon
        };
      case 'svg':
        return {
          svg: `<!-- SVG content for ${icon.icon_key} -->`,
          icon
        };
      default:
        return { icon };
    }
  };

  // 选择图标
  const handleIconSelect = (icon: IconMetadata | any) => {
    // 处理不同的数据结构：普通图标和收藏夹图标
    const libraryName = icon.library?.name || icon.library_name || '';
    const iconValue = `${libraryName}:${icon.icon_key}`;

    if (mode === 'insert') {
      if (multiple) {
        // 多选模式
        setSelectedIcons(prev => {
          const exists = prev.find(i => i.id === icon.id);
          if (exists) {
            return prev.filter(i => i.id !== icon.id);
          } else {
            return [...prev, icon];
          }
        });
      } else {
        // 单选插入模式，直接插入
        const insertData = generateInsertData(icon, insertMode);
        onInsert?.(insertData);
        message.success('图标已插入');
        onClose();
        return;
      }
    } else {
      // 传统选择模式
      setSelectedIcon(iconValue);
    }
  };

  // 确认选择/插入
  const handleConfirm = () => {
    if (mode === 'insert' && multiple && selectedIcons.length > 0) {
      // 多选插入模式
      const insertDataList = selectedIcons.map(icon => generateInsertData(icon, insertMode));
      insertDataList.forEach(data => onInsert?.(data));
      message.success(`已插入 ${selectedIcons.length} 个图标`);
    } else if (mode === 'select' && selectedIcon) {
      // 传统选择模式
      onSelect?.(selectedIcon);
      message.success('图标选择成功');
    }
    onClose();
  };

  // 检查图标是否在收藏夹中
  const isIconFavorited = (iconId: number) => {
    return favoriteIcons.some((favIcon: any) => favIcon.id === iconId);
  };

  // 切换收藏
  const handleToggleFavorite = (icon: IconMetadata, e: React.MouseEvent) => {
    e.stopPropagation();
    const isFavorite = isIconFavorited(icon.id);
    favoriteMutation.mutate({
      iconId: icon.id,
      isFavorite: isFavorite
    });
  };

  // 构建分类树数据
  const buildCategoryTree = (categories: IconCategory[]): any[] => {
    if (!categories) return [];

    const tree = categories
      .filter(cat => !cat.parent_id)
      .map(cat => ({
        title: cat.display_name,
        key: cat.id.toString(),
        value: cat.id,
        children: categories
          .filter(child => child.parent_id === cat.id)
          .map(child => ({
            title: child.display_name,
            key: child.id.toString(),
            value: child.id
          }))
      }));

    return tree;
  };

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSelectedIcon(value || null);
      setCurrentPage(1);
      setActiveTab('browse');
      setSearchFilters({});
    }
  }, [visible, value]);

  // 当切换到收藏夹标签页时，自动设置收藏筛选
  useEffect(() => {
    if (activeTab === 'favorites') {
      setSearchFilters(prev => ({ ...prev, favorites_only: true }));
      setCurrentPage(1);
    } else if (activeTab === 'browse') {
      setSearchFilters(prev => ({ ...prev, favorites_only: false }));
      setCurrentPage(1);
    }
  }, [activeTab]);

  const icons = searchResult?.icons || [];
  const total = searchResult?.total || 0;
  const categoryTree = buildCategoryTree(categories || []);

  // 构建底部按钮
  const footerButtons = [
    <Button key="cancel" onClick={onClose}>取消</Button>,
    <Button key="clear" onClick={handleClearFilters} icon={<ClearOutlined />}>
      清除筛选
    </Button>
  ];

  // 根据模式添加确认按钮
  if (mode === 'insert' && multiple) {
    if (selectedIcons.length > 0) {
      footerButtons.push(
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
        >
          确定插入 ({selectedIcons.length})
        </Button>
      );
    }
  } else if (mode === 'select') {
    footerButtons.push(
      <Button
        key="confirm"
        type="primary"
        onClick={handleConfirm}
        disabled={!selectedIcon}
      >
        确认选择
      </Button>
    );
  }

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      width={width}
      style={{ top: 20 }}
      footer={<Space>{footerButtons}</Space>}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'browse',
            label: (
              <span>
                <AppstoreOutlined />
                浏览图标
                {iconStats && <Badge count={iconStats.total_icons} style={{ marginLeft: 8 }} />}
              </span>
            ),
            children: (
              <div>
                {/* 搜索和筛选区域 */}
                <div style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Search
                  placeholder="搜索图标名称、标签..."
                  allowClear
                  value={localSearchValue}
                  onChange={handleSearchChange}
                  onSearch={handleSearch}
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col span={6}>
                <Select
                  placeholder="选择图标库"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={handleLibraryFilter}
                  value={searchFilters.library_id}
                  loading={librariesLoading}
                >
                  {libraries?.map((lib: IconLibrary) => (
                    <Option key={lib.id} value={lib.id}>
                      <Space>
                        {lib.display_name}
                        <Tag size="small">{lib.name}</Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="选择分类"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={handleCategoryFilter}
                  value={searchFilters.category_id}
                  disabled={!searchFilters.library_id}
                >
                  {categories?.map((cat: IconCategory) => (
                    <Option key={cat.id} value={cat.id}>
                      <Space>
                        {cat.icon && (
                          <UniversalIconRenderer
                            library={libraries?.find(lib => lib.id === cat.library_id)?.name || ''}
                            iconKey={cat.icon}
                            size={16}
                            theme={theme}
                          />
                        )}
                        {cat.display_name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <Button
                  type={searchFilters.favorites_only ? 'primary' : 'default'}
                  icon={searchFilters.favorites_only ? <StarFilled /> : <StarOutlined />}
                  onClick={() => setSearchFilters(prev => ({
                    ...prev,
                    favorites_only: !prev.favorites_only
                  }))}
                  style={{ width: '100%' }}
                >
                  收藏
                </Button>
              </Col>
                </Row>
                </div>

                {/* 图标显示区域 */}
                <div style={{ minHeight: 400, maxHeight: 500, overflowY: 'auto' }}>
                  {iconsLoading ? (
                    <div style={{ textAlign: 'center', padding: 50 }}>
                      <Spin size="large" tip="加载图标中..." />
                    </div>
                  ) : icons.length === 0 ? (
                    <Empty
                      description={
                        searchFilters.search || searchFilters.library_id || searchFilters.category_id
                          ? '未找到匹配的图标'
                          : '暂无图标数据'
                      }
                    />
                  ) : (
                    <Row gutter={[8, 8]}>
                      {icons.map((icon: IconMetadata) => {
                        const iconValue = `${icon.library?.name}:${icon.icon_key}`;
                        const isSelected = mode === 'select'
                          ? selectedIcon === iconValue
                          : selectedIcons.some(i => i.id === icon.id);

                        return (
                          <Col key={icon.id} span={4}>
                            <Card
                              size="small"
                              hoverable
                              className={`icon-card ${isSelected ? 'selected' : ''}`}
                              style={{
                                textAlign: 'center',
                                cursor: 'pointer',
                                border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
                                backgroundColor: isSelected ? '#e6f7ff' : 'white'
                              }}
                              onClick={() => handleIconSelect(icon)}
                              bodyStyle={{ padding: 8 }}
                            >
                              <div style={{ position: 'relative' }}>
                                <UniversalIconRenderer
                                  library={icon.library?.name || ''}
                                  iconKey={icon.icon_key}
                                  size={size}
                                  theme={theme}
                                />

                                {/* 收藏按钮 */}
                                <Button
                                  type="text"
                                  size="small"
                                  icon={isIconFavorited(icon.id) ?
                                    <StarFilled style={{ color: '#faad14' }} /> :
                                    <StarOutlined style={{ color: '#8c8c8c' }} />
                                  }
                                  style={{
                                    position: 'absolute',
                                    top: -4,
                                    right: -4,
                                    padding: 2,
                                    minWidth: 20,
                                    height: 20,
                                    opacity: 0.7
                                  }}
                                  onClick={(e) => handleToggleFavorite(icon, e)}
                                />
                              </div>

                              <Tooltip title={`${icon.display_name || icon.icon_key} (${icon.library?.display_name})`}>
                                <div style={{
                                  fontSize: 10,
                                  marginTop: 4,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                  color: '#666'
                                }}>
                                  {icon.display_name || icon.icon_key}
                                </div>
                              </Tooltip>
                            </Card>
                          </Col>
                        );
                      })}
                    </Row>
                  )}
                </div>

                {/* 分页 */}
                {searchResult && searchResult.total > pageSize && (
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Pagination
                      current={currentPage}
                      pageSize={pageSize}
                      total={searchResult.total}
                      onChange={(page) => setCurrentPage(page)}
                      showSizeChanger={false}
                      showQuickJumper
                      showTotal={(total, range) =>
                        `第 ${range[0]}-${range[1]} 项，共 ${total} 个图标`
                      }
                    />
                  </div>
                )}
              </div>
            )
          },

          {
            key: 'favorites',
            label: (
              <span>
                <HeartOutlined />
                收藏夹
                {iconStats && <Badge count={iconStats.favorite_count} style={{ marginLeft: 8 }} />}
              </span>
            ),
            children: (
              <div>
                {/* 收藏夹选择器 */}
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Select
                        placeholder="选择收藏夹"
                        value={selectedFavoriteFolder}
                        onChange={setSelectedFavoriteFolder}
                        style={{ width: '100%' }}
                        allowClear
                      >
                        <Option value={null}>所有收藏夹</Option>
                        {favoriteFolders.map((folder: any) => (
                          <Option key={folder.id} value={folder.id}>
                            <Space>
                              <div
                                style={{
                                  width: '12px',
                                  height: '12px',
                                  backgroundColor: folder.color,
                                  borderRadius: '2px',
                                  display: 'inline-block'
                                }}
                              />
                              {folder.display_name}
                              <Badge count={folder.icon_count} size="small" />
                            </Space>
                          </Option>
                        ))}
                      </Select>
                    </Col>
                    <Col span={12}>
                      <Search
                        placeholder="在收藏中搜索..."
                        allowClear
                        onSearch={(value) => {
                          // 这里可以添加搜索逻辑
                        }}
                        prefix={<SearchOutlined />}
                      />
                    </Col>
                  </Row>
                </div>

                {/* 收藏夹图标展示 */}
                {favoriteIcons.length > 0 ? (
                  <Row gutter={[8, 8]}>
                    {favoriteIcons.map((icon: any) => {
                      const iconValue = `${icon.library_name}:${icon.icon_key}`;
                      const isSelected = mode === 'select'
                        ? selectedIcon === iconValue
                        : selectedIcons.some(i => i.id === icon.id);

                return (
                  <Col key={icon.id} span={4}>
                    <Card
                      size="small"
                      hoverable
                      className={`icon-card ${isSelected ? 'selected' : ''}`}
                      style={{
                        textAlign: 'center',
                        cursor: 'pointer',
                        border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
                        backgroundColor: isSelected ? '#e6f7ff' : 'white'
                      }}
                      onClick={() => handleIconSelect(icon)}
                      bodyStyle={{ padding: 8 }}
                    >
                      <div style={{ position: 'relative' }}>
                        <UniversalIconRenderer
                          library={icon.library_name || ''}
                          iconKey={icon.icon_key}
                          size={size}
                          theme={theme}
                        />

                        {/* 收藏按钮 */}
                        <Button
                          type="text"
                          size="small"
                          icon={<StarFilled style={{ color: '#faad14' }} />}
                          style={{
                            position: 'absolute',
                            top: -4,
                            right: -4,
                            padding: 2,
                            minWidth: 20,
                            height: 20,
                            opacity: 0.7
                          }}
                          onClick={(e) => handleToggleFavorite(icon, e)}
                        />
                      </div>

                      <div style={{
                        fontSize: '12px',
                        marginTop: 4,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {icon.display_name || icon.icon_key}
                      </div>
                    </Card>
                  </Col>
                );
              })}
            </Row>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                    {foldersLoading || favoriteIconsLoading ? (
                      <div>
                        <Spin size="large" />
                        <div style={{ marginTop: 16 }}>
                          {foldersLoading ? '加载收藏夹...' : '加载收藏图标...'}
                        </div>
                      </div>
                    ) : favoriteFolders.length === 0 ? (
                      <div>
                        <div style={{ marginBottom: 16 }}>暂无收藏夹</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          请先创建收藏夹并添加图标
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div style={{ marginBottom: 16 }}>暂无收藏的图标</div>
                        {selectedFavoriteFolder ? (
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            当前收藏夹中没有图标
                          </div>
                        ) : (
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            所有收藏夹中都没有图标
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )
          }

        ]}
      />

      {/* 当前选择的图标 */}
      {selectedIcon && (
        <div style={{
          marginBottom: 16,
          padding: 12,
          background: '#f0f9ff',
          borderRadius: 6,
          border: '1px solid #91d5ff'
        }}>
          <Space>
            <span style={{ fontWeight: 500 }}>当前选择:</span>
            <UniversalIconRenderer
              library={selectedIcon.split(':')[0]}
              iconKey={selectedIcon.split(':')[1]}
              size={24}
              theme={theme}
            />
            <Tag color="blue">{selectedIcon}</Tag>
          </Space>
        </div>
      )}
    </Modal>
  );
};

// 为了向后兼容，保留原有的IconSelector导出
export default IconSelector;

// 同时导出为UniversalIconSelector，用于替代原来的UniversalIconSelector组件
export { IconSelector as UniversalIconSelector };
