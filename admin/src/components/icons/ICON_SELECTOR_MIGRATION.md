# 图标选择器组件整合迁移指南

## 📋 整合概述

我们已经将 `UniversalIconSelector.tsx` 的功能整合到 `IconSelector.tsx` 中，实现了统一的图标选择器组件。

## 🔄 迁移步骤

### 1. 组件导入更新

**之前的导入方式:**
```typescript
// 旧的导入方式
import IconSelector from './components/icons/IconSelector';
import UniversalIconSelector from './components/icons/UniversalIconSelector';
```

**新的导入方式:**
```typescript
// 新的统一导入方式
import IconSelector, { UniversalIconSelector } from './components/icons/IconSelector';
// 或者直接使用
import IconSelector from './components/icons/IconSelector';
```

### 2. 组件使用方式更新

#### 传统选择模式 (原IconSelector功能)
```typescript
<IconSelector
  visible={visible}
  onClose={onClose}
  onSelect={(iconValue: string) => {
    // 处理选择的图标值，格式: "library:icon_key"
    console.log('Selected icon:', iconValue);
  }}
  value={currentIcon}
  size={32}
  theme="light"
  title="选择图标"
/>
```

#### 插入模式 (原UniversalIconSelector功能)
```typescript
<IconSelector
  visible={visible}
  onClose={onClose}
  mode="insert"
  insertMode="markdown" // 'markdown' | 'component' | 'url' | 'svg'
  multiple={false}
  onInsert={(iconData: IconInsertData) => {
    // 处理插入的图标数据
    console.log('Insert data:', iconData);
    // iconData.markdown - Markdown格式
    // iconData.component - React组件格式
    // iconData.url - 图标URL
    // iconData.svg - SVG代码
  }}
  libraries={['lucide', 'iconic', 'iconify']}
  categories={[]}
  title="插入图标"
  width={1200}
  height={700}
/>
```

#### 多选插入模式
```typescript
<IconSelector
  visible={visible}
  onClose={onClose}
  mode="insert"
  insertMode="component"
  multiple={true}
  onInsert={(iconData: IconInsertData) => {
    // 每个选中的图标都会调用一次onInsert
    console.log('Insert icon:', iconData);
  }}
  title="批量插入图标"
/>
```

### 3. 接口类型更新

#### 新的Props接口
```typescript
interface IconSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect?: (iconValue: string) => void;        // 选择模式回调
  onInsert?: (iconData: IconInsertData) => void; // 插入模式回调
  value?: string;
  size?: number;
  theme?: 'light' | 'dark';
  title?: string;
  
  // 新增的功能参数
  mode?: 'select' | 'insert';                    // 模式选择
  insertMode?: 'markdown' | 'component' | 'url' | 'svg'; // 插入格式
  multiple?: boolean;                            // 是否多选
  libraries?: string[];                          // 图标库筛选
  categories?: string[];                         // 分类筛选
  width?: number;                               // 弹窗宽度
  height?: number;                              // 弹窗高度
}

interface IconInsertData {
  markdown?: string;   // Markdown格式: ![name](url)
  component?: string;  // 组件格式: <CustomIcon name="lib:key" />
  url?: string;        // 图标URL
  svg?: string;        // SVG代码
  icon: IconMetadata;  // 图标元数据
}
```

### 4. 功能对比

| 功能 | 原IconSelector | 原UniversalIconSelector | 新IconSelector |
|------|---------------|------------------------|---------------|
| 基础选择 | ✅ | ❌ | ✅ |
| 高级搜索 | ✅ | ❌ | ✅ |
| 分类浏览 | ✅ | ✅ | ✅ |
| 收藏功能 | ✅ | ✅ | ✅ |
| 多种插入格式 | ❌ | ✅ | ✅ |
| 多选功能 | ❌ | ✅ | ✅ |
| 自定义尺寸 | ✅ | ❌ | ✅ |

### 5. 迁移检查清单

- [ ] 更新所有导入语句
- [ ] 更新组件使用方式
- [ ] 测试选择模式功能
- [ ] 测试插入模式功能
- [ ] 测试多选功能
- [ ] 验证收藏功能正常
- [ ] 检查搜索和筛选功能
- [ ] 删除旧的UniversalIconSelector文件

## 🗑️ 清理工作

### 可以删除的文件
- `admin/src/components/icons/UniversalIconSelector.tsx`
- `admin/src/components/icons/UniversalIconSelector.css`

### 需要更新的文件
- 所有引用UniversalIconSelector的组件文件
- 相关的类型定义文件
- 测试文件

## 📈 优化效果

- **代码减少**: 从 900+ 行减少到 800+ 行 (节省约100行)
- **功能统一**: 一个组件支持所有图标选择场景
- **维护简化**: 只需维护一个图标选择器组件
- **API一致**: 统一的接口和使用方式

## 🔧 故障排除

### 常见问题

1. **类型错误**: 确保导入了正确的类型定义
2. **回调函数**: 注意区分onSelect和onInsert的使用场景
3. **模式切换**: 确保mode参数设置正确
4. **多选状态**: 多选模式下需要手动点击确认按钮

### 调试建议

```typescript
// 添加调试日志
<IconSelector
  onSelect={(iconValue) => {
    console.log('Selected:', iconValue);
  }}
  onInsert={(iconData) => {
    console.log('Inserted:', iconData);
  }}
  // ... 其他props
/>
```

## 📞 支持

如果在迁移过程中遇到问题，请检查:
1. 组件props是否正确设置
2. 回调函数是否正确处理
3. 类型定义是否匹配
4. 控制台是否有错误信息
