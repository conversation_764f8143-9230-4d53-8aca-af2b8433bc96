/**
 * 图标选择器Hook
 */

import React, { useState, useCallback } from 'react';
import { IconInsertData, IconMetadata } from '../types/icon';
import { UniversalIconSelector } from '../components/icons/IconSelector';

interface UseIconSelectorOptions {
  mode?: 'markdown' | 'component' | 'url' | 'svg';
  multiple?: boolean;
  libraries?: string[];
  categories?: string[];
  title?: string;
  width?: number;
  height?: number;
  onInsert?: (data: IconInsertData | IconInsertData[]) => void;
}

export const useIconSelector = (options: UseIconSelectorOptions = {}) => {
  const [visible, setVisible] = useState(false);
  const [selectedIcons, setSelectedIcons] = useState<IconMetadata[]>([]);

  const openSelector = useCallback(() => {
    setVisible(true);
    setSelectedIcons([]);
  }, []);

  const closeSelector = useCallback(() => {
    setVisible(false);
    setSelectedIcons([]);
  }, []);

  const handleInsert = useCallback((data: IconInsertData) => {
    if (options.onInsert) {
      if (options.multiple) {
        // 多选模式下收集所有插入的数据
        options.onInsert(data);
      } else {
        // 单选模式直接调用
        options.onInsert(data);
      }
    }

    if (!options.multiple) {
      closeSelector();
    }
  }, [options.onInsert, options.multiple, closeSelector]);

  const IconSelectorComponent = useCallback(() => (
    <UniversalIconSelector
      visible={visible}
      onClose={closeSelector}
      onInsert={handleInsert}
      mode="insert"
      insertMode={options.mode}
      multiple={options.multiple}
      libraries={options.libraries}
      categories={options.categories}
      title={options.title}
      width={options.width}
      height={options.height}
    />
  ), [
    visible,
    closeSelector,
    handleInsert,
    options.mode,
    options.multiple,
    options.libraries,
    options.categories,
    options.title,
    options.width,
    options.height
  ]);

  return {
    openSelector,
    closeSelector,
    IconSelectorComponent,
    selectedIcons,
    isVisible: visible
  };
};

// 便捷的预设Hook
export const useMarkdownIconSelector = (onInsert?: (markdown: string) => void) => {
  return useIconSelector({
    mode: 'markdown',
    multiple: false,
    onInsert: (data) => {
      if (data.markdown && onInsert) {
        onInsert(data.markdown);
      }
    }
  });
};

export const useComponentIconSelector = (onInsert?: (component: string) => void) => {
  return useIconSelector({
    mode: 'component',
    multiple: false,
    onInsert: (data) => {
      if (data.component && onInsert) {
        onInsert(data.component);
      }
    }
  });
};

export const useUrlIconSelector = (onInsert?: (url: string) => void) => {
  return useIconSelector({
    mode: 'url',
    multiple: false,
    onInsert: (data) => {
      if (data.url && onInsert) {
        onInsert(data.url);
      }
    }
  });
};

export const useSvgIconSelector = (onInsert?: (svg: string) => void) => {
  return useIconSelector({
    mode: 'svg',
    multiple: false,
    onInsert: (data) => {
      if (data.svg && onInsert) {
        onInsert(data.svg);
      }
    }
  });
};

export default useIconSelector;
