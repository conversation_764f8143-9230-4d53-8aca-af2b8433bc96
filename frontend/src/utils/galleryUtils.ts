import axios from 'axios';
import { API_BASE_URL } from '../lib/api';

/**
 * Image data type
 */
export type ImageData = {
  id: string
  url: string
  thumbnail_url?: string
  webp_url?: string
  thumbnail_webp_url?: string
  alt: string
  width: number
  height: number
  date: string
  location?: string
  tags?: string[]
  caption?: string
  // 新增字段
  display_name?: string
  description?: string
  category_id?: number
  file_size?: number
  mime_type?: string
  original_filename?: string
  upload_source?: string
  external_id?: string
  image_metadata?: any
  view_count?: number
  download_count?: number
  created_at?: string
  updated_at?: string
  category?: {
    id: number
    name: string
    slug: string
    color: string
    icon?: string
  }
}

/**
 * Album data type
 */
export type AlbumData = {
  id: string
  title: string
  description?: string
  coverImage: ImageData
  images: ImageData[]
  date: string
  location?: string
  tags?: string[]
  slug?: string // 添加slug字段，用于分类相册的URL跳转
}

/**
 * Timeline entry type
 */
export type TimelineEntry = {
  id: string
  title?: string
  slug?: string
  content: string
  images: ImageData[]
  date: string
  location?: string
  tags?: string[]
}

/**
 * Gallery category type
 */
export type GalleryCategory = {
  id: number
  name: string
  slug: string
  description?: string
  layout_type: string
  columns: number
  image_ratio: string
  item_count: number
  cover_image?: any
}

/**
 * Album type (for backend API response)
 */
export type Album = {
  id: number | string
  title: string
  description?: string
  cover_image?: {
    id: number
    url: string
    thumbnail_url?: string
    webp_url?: string
    thumbnail_webp_url?: string
    alt?: string
    width?: number
    height?: number
    date?: string
    location?: string
    caption?: string
    storage_type?: string
    display_name?: string
    description?: string
    category_id?: number
    file_size?: number
    mime_type?: string
    original_filename?: string
    upload_source?: string
    external_id?: string
    image_metadata?: any
    view_count?: number
    download_count?: number
    created_at?: string
    updated_at?: string
    tags?: any[]
    category?: {
      id: number
      name: string
      slug: string
      color: string
      icon?: string
    }
  } | null
  images?: ImageData[]
  date: string
  location?: string
  category?: string
  slug?: string
  layout_type?: string
  is_featured?: boolean
  sort_order?: number
  created_at: string
  updated_at?: string
}

/**
 * Gallery item type
 */
export type GalleryItem = {
  id: number
  title?: string
  description?: string
  overlay_text?: string
  link_url?: string
  custom_width?: number
  custom_height?: number
  view_count: number
  like_count: number
  image: {
    id: number
    url: string
    thumbnail_url?: string
    display_name?: string
    original_filename: string
    width?: number
    height?: number
    file_size?: number
  }
}

/**
 * Load albums from unified Albums API
 */
export async function loadAlbumsFromApi(): Promise<AlbumData[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/images/albums`);
    const albums = response.data;

    return albums.map((album: any) => ({
      id: album.id.toString(),
      title: album.title,
      description: album.description,
      coverImage: album.cover_image ? {
        id: album.cover_image.id.toString(),
        url: album.cover_image.url,
        thumbnail_url: album.cover_image.thumbnail_url,
        alt: album.cover_image.display_name || album.title,
        width: album.cover_image.width || 400,
        height: album.cover_image.height || 300,
        date: album.created_at || new Date().toISOString(),
        tags: [],
      } : {
        id: '0',
        url: '',
        thumbnail_url: '',
        alt: album.title,
        width: 400,
        height: 300,
        date: album.created_at || new Date().toISOString(),
        tags: [],
      },
      images: album.images?.map((img: any) => ({
        id: img.id.toString(),
        url: img.url,
        thumbnail_url: img.thumbnail_url,
        webp_url: img.webp_url,
        thumbnail_webp_url: img.thumbnail_webp_url,
        alt: img.display_name || img.original_filename,
        width: img.width || 400,
        height: img.height || 300,
        date: img.created_at || album.date,
        location: img.location || album.location,
        tags: img.tags?.map((tag: any) => tag.name) || [],
        caption: img.description,
      })) || [],
      date: album.date,
      location: album.location,
      tags: [], // 可以后续从相册图片的tags中提取
      slug: album.slug, // 添加slug支持
    }));
  } catch (error) {
    console.error('Error loading albums from API:', error);
    return [];
  }
}

/**
 * Load albums by category from unified Albums API
 */
export async function loadAlbumsByCategory(categoryName: string): Promise<AlbumData[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/images/albums/categories/${categoryName}/albums`);
    const albums = response.data;

    return albums.map((album: any) => ({
      id: album.id.toString(),
      title: album.title,
      description: album.description,
      coverImage: album.cover_image ? {
        id: album.cover_image.id.toString(),
        url: album.cover_image.url,
        thumbnail_url: album.cover_image.thumbnail_url,
        alt: album.cover_image.display_name || album.title,
        width: album.cover_image.width || 400,
        height: album.cover_image.height || 300,
        date: album.created_at || new Date().toISOString(),
        tags: [],
      } : {
        id: '0',
        url: '',
        thumbnail_url: '',
        alt: album.title,
        width: 400,
        height: 300,
        date: album.created_at || new Date().toISOString(),
        tags: [],
      },
      images: [], // 不在列表页加载所有图片，只在详情页加载
      date: album.date,
      location: album.location,
      tags: [],
      slug: album.slug,
    }));
  } catch (error) {
    console.error('Error loading albums by category:', error);
    return [];
  }
}

/**
 * Load album categories from unified Albums API
 */
export async function loadAlbumCategories(): Promise<{
  id: number;
  name: string;
  description?: string;
  album_count: number;
}[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/images/albums/categories`);
    return response.data;
  } catch (error) {
    console.error('Error loading album categories:', error);
    return [];
  }
}



/**
 * Load album detail from unified Albums API (supports ID or slug)
 */
export async function loadAlbumDetail(albumIdentifier: string): Promise<AlbumData> {
  try {
    const response = await axios.get(`${API_BASE_URL}/images/albums/${albumIdentifier}`);
    const album = response.data;

    // Convert to AlbumData format
    return {
      id: album.id.toString(),
      title: album.title,
      description: album.description,
      coverImage: album.cover_image ? {
        id: album.cover_image.id.toString(),
        url: album.cover_image.url,
        thumbnail_url: album.cover_image.thumbnail_url,
        alt: album.cover_image.display_name || album.title,
        width: album.cover_image.width || 400,
        height: album.cover_image.height || 300,
        date: album.created_at || new Date().toISOString(),
        tags: [],
      } : {
        id: '0',
        url: '',
        thumbnail_url: '',
        alt: album.title,
        width: 400,
        height: 300,
        date: album.created_at || new Date().toISOString(),
        tags: [],
      },
      images: album.images?.map((img: any) => ({
        id: img.id.toString(),
        url: img.url,
        thumbnail_url: img.thumbnail_url,
        webp_url: img.webp_url,
        thumbnail_webp_url: img.thumbnail_webp_url,
        alt: img.display_name || img.original_filename,
        width: img.width || 400,
        height: img.height || 300,
        date: img.created_at || album.date,
        location: img.location || album.location,
        tags: img.tags?.map((tag: any) => tag.name) || [],
        caption: img.description,
      })) || [],
      date: album.date,
      location: album.location,
      tags: album.images?.flatMap((img: any) => img.tags?.map((tag: any) => tag.name) || []) || [],
      slug: album.slug,
    };
  } catch (error) {
    console.error('Error loading album detail:', error);
    throw error;
  }
}




/**
 * Load timeline entries from Gallery API
 */
export async function loadTimelineEntries(): Promise<TimelineEntry[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/gallery/public/timeline`);
    return response.data.map((entry: any) => ({
      id: entry.id.toString(),
      title: entry.title,
      content: entry.content,
      images: entry.images?.map((img: any) => ({
        id: img.id.toString(),
        url: img.url,
        thumbnail_url: img.thumbnail_url,
        webp_url: img.webp_url,
        thumbnail_webp_url: img.thumbnail_webp_url,
        alt: img.display_name || img.original_filename,
        width: img.width || 400,
        height: img.height || 300,
        date: img.created_at || entry.created_at,
        location: img.location || entry.location,
        tags: img.tags?.map((tag: any) => tag.name) || [],
        caption: img.description,
      })) || [],
      date: entry.created_at,
      location: entry.location,
      tags: [],
    }));
  } catch (error) {
    console.error('Error loading timeline entries:', error);
    return [];
  }
}

/**
 * Organize images into timeline entries (utility function)
 */
export function organizeImagesIntoTimeline(images: ImageData[]): TimelineEntry[] {
  const groupedByDate: Record<string, ImageData[]> = {}

  // Group by date
  images.forEach(image => {
    const dateKey = image.date
    if (!groupedByDate[dateKey]) {
      groupedByDate[dateKey] = []
    }
    groupedByDate[dateKey].push(image)
  })

  // Convert groups to timeline entries
  const timeline = Object.entries(groupedByDate).map(([date, dateImages]) => {
    // Get common location for the day's photos (if any)
    const locations = dateImages
      .map(img => img.location)
      .filter((loc): loc is string => !!loc)

    const uniqueLocations = [...new Set(locations)]
    const location = uniqueLocations.length === 1 ? uniqueLocations[0] : undefined

    // Get common tags for the day's photos
    const allTags = dateImages.flatMap(img => img.tags || [])
    const uniqueTags = [...new Set(allTags)]

    return {
      id: `timeline-${date}`,
      content: new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      images: dateImages,
      date,
      location,
      tags: uniqueTags,
    }
  })

  // Sort by date (newest first)
  return timeline.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

/**
 * Load and process gallery data from unified APIs
 */
export async function loadGalleryData() {
  // Load timeline entries from Gallery API
  const timelineEntries = await loadTimelineEntries();

  // Load albums from unified Albums API
  const albums = await loadAlbumsFromApi();

  // Load album categories
  const categories = await loadAlbumCategories();

  return {
    albums,
    timelineEntries,
    categories,
    allImages: albums.flatMap(album => album.images),
  };
}
